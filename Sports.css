*{
    margin: 0;
    padding: 0;
}
/*This is the body part editing*/
.body{
    background-color: rgb(245,245,245);
}
.hide{
    height: 38px;
}
/*This is the button div class*/
.btt{
    background-color: rgb(75,0,130);
    height: 38px;
    width: 100%;
    position: fixed;
    top: 0;
}
/*This is the button class*/ 
.bttn{
    height: 20px;
    width: 28px;
    margin: 8px;
    background-image: url(arrow.png);
    background-repeat: no-repeat;
    background-position: center;
    background-color: rgb(75,0,130);
    border: 1px solid rgba(255, 255, 255, 0.548);
    border-radius: 4px;
    box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
}
/*This is the div class of t-shirt,trouser,vest and half pant heading*/
.bar{
    margin: 10px 0px;
    height: 35px;
    width: 100%;
    background-color: rgb(245,255,250);
    box-shadow: 3px 3px 3px 3px grey;
    border-radius: 7px;
}
/*This is the fonts desgine of item heading*/
.txt{
    margin: 0px 10px;
    font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}
/*all pictures div section name is tt*/
.tt{
    height: 279px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    overflow: scroll;
    scroll-behavior: smooth;
}
/*This is the t-shirt section images*/
img.t{
    margin: 11px;
    padding: 0;
    height: 177px;
    box-shadow: 0px 5px 5px 0px grey;
}
.dec{
    align-items: center;
}
/*this is the t-shirt item names and description*/
p.decc{
    display: block;
    text-align: center;
    font-family: sans-serif;
    font-size: 15px;
    margin: 0px 5px;
    

}
/*this is the  Trouser image section*/
img.simg{
    margin: 11px;
    padding: 0;
    height: 177px;
    box-shadow: 0px 5px 5px 0px grey;
}
/*tis is the Trouser item name and description*/
p.sdec{
    margin: 0px 5px;
    display: block;
    text-align: center;
    font-family: sans-serif;
    font-size: 15px;
    justify-content: center;
}
/*this is the  Vest image section*/
img.jimg{
    margin: 11px;
    padding: 0;
    height: 177px;
    box-shadow: 0px 5px 5px 0px grey;
}
/*tis is the vest item name and description*/
p.jdec{
    display: block;
    text-align: center;
    font-family: sans-serif;
    font-size: 15px;
    margin: 0px 5px;
    justify-content: center;
}
/*this is the  Halfpant image section*/
img.himg{
    margin: 11px;
    padding: 0;
    height: 177px;
    box-shadow: 0px 5px 5px 0px grey;
}
/*tis is the Halfpant item name and description*/
p.hdec{
    display: block;
    text-align: center;
    font-family: sans-serif;
    font-size: 15px;
    margin: 0px 5px;
    justify-content: center;
}
/*this is the hover item section*/
.t:hover, .simg:hover, .jimg:hover, .himg:hover{
    height:180px
}
@media (max-width: 450px){
    *{
        margin: 0;
        padding: 0;
    }
    /*This is the body part editing*/
    .body{
        background-color: rgb(245,245,245);
    }
    /*This is the button div class*/
    .btt{
        background-color: rgb(75,0,130);
        height: 38px;
        width: 100%;
        position: fixed;
        top: 0;
    }
    .hide{
        height: 38px;
    }
    /*This is the button class*/ 
    .bttn{
        height: 18px;
        width: 28px;
        margin: 8px;
        background-image: url(arrow.png);
        background-repeat: no-repeat;
        background-position: center;
        background-color: rgb(75,0,130);
        border: 1px solid rgba(255, 255, 255, 0.548);
        border-radius: 4px;
        box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
        background-size: 20px
    }
    /*This is the div class of t-shirt,shirt,jence and half pant heading*/
    .bar{
        margin: 10px 0px;
        height: 26px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 1px 1px 1px 1px gray;
        border-radius: 5px;
    }
    /*This is the fonts desgine of item heading*/
    .txt{
        margin: 0px 5px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-size: 15px;
    }
    /*all pictures div section name is tt*/
    .tt{
        height: 190px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    /*This is the t-shirt section images*/
    img.t{
        margin: 11px;
        padding: 0;
        height: 110px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*this is the t-shirt item names and description*/
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 12px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  shirt image section*/
    img.simg{
        margin: 11px;
        padding: 0;
        height: 110px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the shirt item name and description*/
    p.sdec{
        margin: 0px 5px;
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 12px;
        justify-content: center;
    }
    /*this is the  jeance image section*/
    img.jimg{
        margin: 11px;
        padding: 0;
        height: 110px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the jeance item name and description*/
    p.jdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 12px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  Halfpant image section*/
    img.himg{
        margin: 11px;
        padding: 0;
        height: 110px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the Halfpant item name and description*/
    p.hdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 12px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the hover item section*/
    .t:hover, .simg:hover, .jimg:hover, .himg:hover{
        height:115px
    }
}
@media (min-width: 470px){
    *{
        margin: 0;
        padding: 0;
    }
    /*This is the body part editing*/
    .body{
        background-color: rgb(245,245,245);
    }
    /*This is the button div class*/
    .btt{
        background-color: rgb(75,0,130);
        height: 38px;
        width: 100%;
        position: fixed;
        top: 0;
    }
    .hide{
        height: 38px;
    }
    /*This is the button class*/ 
    .bttn{
        height: 18px;
        width: 28px;
        margin: 8px;
        background-image: url(arrow.png);
        background-repeat: no-repeat;
        background-position: center;
        background-color: rgb(75,0,130);
        border: 1px solid rgba(255, 255, 255, 0.548);
        border-radius: 4px;
        box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
        background-size: 20px
    }
    /*This is the div class of t-shirt,shirt,jence and half pant heading*/
    .bar{
        margin: 10px 0px;
        height: 27px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 1px 1px 1px 1px gray;
        border-radius: 5px;
    }
    /*This is the fonts desgine of item heading*/
    .txt{
        margin: 0px 5px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-size: 16px;
    }
    /*all pictures div section name is tt*/
    .tt{
        height: 192px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    /*This is the t-shirt section images*/
    img.t{
        margin: 11px;
        padding: 0;
        height: 112px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*this is the t-shirt item names and description*/
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  shirt image section*/
    img.simg{
        margin: 12px;
        padding: 0;
        height: 110px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the shirt item name and description*/
    p.sdec{
        margin: 0px 5px;
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        justify-content: center;
    }
    /*this is the  jeance image section*/
    img.jimg{
        margin: 11px;
        padding: 0;
        height: 112px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the jeance item name and description*/
    p.jdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  Halfpant image section*/
    img.himg{
        margin: 11px;
        padding: 0;
        height: 112px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the Halfpant item name and description*/
    p.hdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the hover item section*/
    .t:hover, .simg:hover, .jimg:hover, .himg:hover{
        height:115px
    }
}
@media (min-width: 575px){
    *{
        margin: 0;
        padding: 0;
    }
    /*This is the body part editing*/
    .body{
        background-color: rgb(245,245,245);
    }
    /*This is the button div class*/
    .btt{
        background-color: rgb(75,0,130);
        height: 38px;
        width: 100%;
        position: fixed;
        top: 0;
    }
    .hide{
        height: 38px;
    }
    /*This is the button class*/ 
    .bttn{
        height: 20px;
        width: 32px;
        margin: 8px;
        background-image: url(arrow.png);
        background-repeat: no-repeat;
        background-position: center;
        background-color: rgb(75,0,130);
        border: 1px solid rgba(255, 255, 255, 0.548);
        border-radius: 4px;
        box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
        background-size: 22px;
    }
    /*This is the div class of t-shirt,shirt,jence and half pant heading*/
    .bar{
        margin: 10px 0px;
        height: 29px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 1px 1px 1px 1px gray;
        border-radius: 5px;
    }
    /*This is the fonts desgine of item heading*/
    .txt{
        margin: 0px 5px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-size: 17px;
    }
    /*all pictures div section name is tt*/
    .tt{
        height: 199px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    /*This is the t-shirt section images*/
    img.t{
        margin: 11px;
        padding: 0;
        height: 115px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*this is the t-shirt item names and description*/
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  shirt image section*/
    img.simg{
        margin: 11px;
        padding: 0;
        height: 115px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the shirt item name and description*/
    p.sdec{
        margin: 0px 5px;
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        justify-content: center;
    }
    /*this is the  jeance image section*/
    img.jimg{
        margin: 11px;
        padding: 0;
        height: 115px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the jeance item name and description*/
    p.jdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  Halfpant image section*/
    img.himg{
        margin: 11px;
        padding: 0;
        height: 115px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the Halfpant item name and description*/
    p.hdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the hover item section*/
    .t:hover, .simg:hover, .jimg:hover, .himg:hover{
        height:119px
    }
}
@media (min-width: 772px){
    *{
        margin: 0;
        padding: 0;
    }
    /*This is the body part editing*/
    .body{
        background-color: rgb(245,245,245);
    }
    .hide{
        height: 38px;
    }
    /*This is the button div class*/
    .btt{
        background-color: rgb(75,0,130);
        height: 38px;
        width: 100%;
        position: fixed;
        top: 0;
    }
    /*This is the button class*/ 
    .bttn{
        height: 20px;
        width: 32px;
        margin: 8px;
        background-image: url(arrow.png);
        background-repeat: no-repeat;
        background-position: center;
        background-color: rgb(75,0,130);
        border: 1px solid rgba(255, 255, 255, 0.548);
        border-radius: 4px;
        box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
        background-size: 23px;
    }
    /*This is the div class of t-shirt,shirt,jence and half pant heading*/
    .bar{
        margin: 10px 0px;
        height: 32px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 1px 1px 1px 1px gray;
        border-radius: 5px;
    }
    /*This is the fonts desgine of item heading*/
    .txt{
        margin: 0px 5px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-size: 18px;
    }
    /*all pictures div section name is tt*/
    .tt{
        height: 212px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    /*This is the t-shirt section images*/
    img.t{
        margin: 11px;
        padding: 0;
        height: 134px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*this is the t-shirt item names and description*/
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 14px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  shirt image section*/
    img.simg{
        margin: 11px;
        padding: 0;
        height: 134px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the shirt item name and description*/
    p.sdec{
        margin: 0px 5px;
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 14px;
        justify-content: center;
    }
    /*this is the  jeance image section*/
    img.jimg{
        margin: 11px;
        padding: 0;
        height: 134px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the jeance item name and description*/
    p.jdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 14px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  Halfpant image section*/
    img.himg{
        margin: 11px;
        padding: 0;
        height: 134px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the Halfpant item name and description*/
    p.hdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 14px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the hover item section*/
    .t:hover, .simg:hover, .jimg:hover, .himg:hover{
        height:138px
    }

}
@media (min-width: 992px){
    *{
        margin: 0;
        padding: 0;
    }
    /*This is the body part editing*/
    .body{
        background-color: rgb(245,245,245);
    }
    .hide{
        height: 38px;
    }
    /*This is the button div class*/
    .btt{
        background-color: rgb(75,0,130);
        height: 38px;
        width: 100%;
        position: fixed;
        top: 0;
    }
    /*This is the button class*/ 
    .bttn{
        height: 20px;
        width: 32px;
        margin: 8px;
        background-image: url(arrow.png);
        background-repeat: no-repeat;
        background-position: center;
        background-color: rgb(75,0,130);
        border: 1px solid rgba(255, 255, 255, 0.548);
        border-radius: 4px;
        box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
        background-size: 24px;
    }
    /*This is the div class of t-shirt,shirt,jence and half pant heading*/
    .bar{
        margin: 10px 0px;
        height: 35px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 1px 1px 1px 1px gray;
        border-radius: 5px;
    }
    /*This is the fonts desgine of item heading*/
    .txt{
        margin: 0px 5px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-size: 20px;
    }
    /*all pictures div section name is tt*/
    .tt{
        height: 217px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    /*This is the t-shirt section images*/
    img.t{
        margin: 11px;
        padding: 0;
        height: 138px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*this is the t-shirt item names and description*/
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  shirt image section*/
    img.simg{
        margin: 11px;
        padding: 0;
        height: 138px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the shirt item name and description*/
    p.sdec{
        margin: 0px 5px;
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        justify-content: center;
    }
    /*this is the  jeance image section*/
    img.jimg{
        margin: 11px;
        padding: 0;
        height: 138px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the jeance item name and description*/
    p.jdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  Halfpant image section*/
    img.himg{
        margin: 11px;
        padding: 0;
        height: 138px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the Halfpant item name and description*/
    p.hdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the hover item section*/
    .t:hover, .simg:hover, .jimg:hover, .himg:hover{
        height:138px
    }
}
@media (min-width: 1200px){
    *{
        margin: 0;
        padding: 0;
    }
    /*This is the body part editing*/
    .hide{
        height: 38px;
    }
    
    /*This is the button div class*/
    .btt{
        background-color: rgb(75,0,130);
        height: 38px;
        width: 100%;
        position: fixed;
        top: 0;
    }
    /*This is the button class*/ 
    .bttn{
        height: 22px;
        width: 37px;
        margin: 8px;
        background-image: url(arrow.png);
        background-repeat: no-repeat;
        background-position: center;
        background-color: rgb(75,0,130);
        border: 1px solid rgba(255, 255, 255, 0.548);
        border-radius: 4px;
        box-shadow: 0px 5px 5px 0px rgb(40, 0, 68);
        background-size: 26px;
    }
    /*This is the div class of t-shirt,shirt,jence and half pant heading*/
    .bar{
        margin: 10px 0px;
        height: 40px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 1px 1px 1px 1px gray;
        border-radius: 5px;
    }
    /*This is the fonts desgine of item heading*/
    .txt{
        margin: 0px 5px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-size: 24px;
    }
    /*all pictures div section name is tt*/
    .tt{
        background-color: rgb(255,250,250);
        height: 254px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    /*This is the t-shirt section images*/
    img.t{
        margin: 11px;
        padding: 0;
        height: 172px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*this is the t-shirt item names and description*/
   
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  shirt image section*/
    img.simg{
        margin: 11px;
        padding: 0;
        height: 172px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the shirt item name and description*/
    p.sdec{
        margin: 0px 5px;
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        justify-content: center;
    }
    /*this is the  jeance image section*/
    img.jimg{
        margin: 11px;
        padding: 0;
        height: 172px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the jeance item name and description*/
    p.jdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the  Halfpant image section*/
    img.himg{
        margin: 11px;
        padding: 0;
        height: 172px;
        box-shadow: 0px 5px 5px 0px grey;
    }
    /*tis is the Halfpant item name and description*/
    p.hdec{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        justify-content: center;
    }
    /*this is the hover item section*/
    .t:hover, .simg:hover, .jimg:hover, .himg:hover{
        height:175px
    }
}