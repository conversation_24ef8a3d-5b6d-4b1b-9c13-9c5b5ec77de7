
@media (max-width: 450px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 16px;
        margin: 4px;
        letter-spacing: 4px;
        text-shadow: 0px 1px 1px white;
        margin-right: 1px;
        width: 208px;
    }
    .title{
        margin: 5px;
        font-size: 8px;
        line-height: 1px;
        letter-spacing: 2px;
    }
    .Search{
        height: 0px;
        width: 0px;
        border-radius: 19px;
        margin: 0px;
        cursor: pointer;
        background: #fffefe;
        visibility: hidden;
    }
    .list{
        list-style: none;
        display: flex;
        visibility: hidden;
        
    }
    .list li{
        margin: 2px 5px;
        letter-spacing: 2px;
        font-size: 8px;
        color: black;
      
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 16rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 11px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 11px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 11px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 10px 0px;
        height: 23px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 2px 2px 2px 2px gray;
        border-radius: 5px;
      }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 1PX;
        font-size: 17px;
    }
    .of{
        height: 132px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 5px;
        height: 82px;
        box-shadow: 0px 2px 2px 0px gray;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 9px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        height: 15px;
        width: 26px;
        margin: 4px;
        background-color: rgb(238, 243, 241);
        background-image: url(left.png);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        border: 1px solid rgb(182, 179, 179);
        border-radius: 4px;
        box-shadow: 0px 2px 2px 0px gray;
        position: absolute;
        left: 90%;
      }
    .bttn:hover{
        height: 16px;
        width: 27px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:84px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 9px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 2em;
        background-color: black;
        color: white;
        margin-top: 20px;
      }
    .cop{
        text-align: center;
        font-size: 8px;
        letter-spacing: 1px;
        line-height: 10px;
    
    }
}
@media (min-width: 470px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 16px;
        margin: 4px;
        letter-spacing: 4px;
        text-shadow: 0px 1px 1px white;
        margin-right: 1px;
        width: 208px;
    }
    .title{
        margin: 5px;
        font-size: 8px;
        line-height: 1px;
        letter-spacing: 2px;
    }
    .Search{
        height: 0px;
        width: 0px;
        border-radius: 19px;
        margin: 0px;
        cursor: pointer;
        background: #fffefe;
        visibility: hidden;
    }
    .list{
        list-style: none;
        display: flex;
        visibility: hidden;
        
    }
    .list li{
        margin: 2px 5px;
        letter-spacing: 2px;
        font-size: 8px;
        color: black;
      
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 16rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 11px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 11px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 11px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 10px 0px;
        height: 23px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 2px 2px 2px 2px gray;
        border-radius: 5px;
      }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 1PX;
        font-size: 17px;
    }
    .of{
        height: 132px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 5px;
        height: 82px;
        box-shadow: 0px 2px 2px 0px gray;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 9px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        height: 15px;
        width: 26px;
        margin: 4px;
        background-color: rgb(238, 243, 241);
        background-image: url(left.png);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        border: 1px solid rgb(182, 179, 179);
        border-radius: 4px;
        box-shadow: 0px 2px 2px 0px gray;
        position: absolute;
        left: 90%;
      }
    .bttn:hover{
        height: 16px;
        width: 27px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:84px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 9px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 2em;
        background-color: black;
        color: white;
        margin-top: 20px;
      }
    .cop{
        text-align: center;
        font-size: 8px;
        letter-spacing: 1px;
        line-height: 14px;
    
    }
}
@media (min-width: 575px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 21px;
        margin: 4px;
        letter-spacing: 4px;
        text-shadow: 0px 1px 1px white;
        margin-right: 31px;
        width: 284px;
       
    }
    .title{
        margin: 5px;
        font-size: 12px;
        line-height: 1px;
        letter-spacing: 2px;
    }
    .Search{
        height: 0px;
        width: 0px;
        border-radius: 19px;
        margin: 0px;
        cursor: pointer;
        background: #fffefe;
        visibility: hidden;
    }
    .list{
        list-style: none;
        display: flex;
        margin-left: 10px;
       visibility: hidden;
    }
    .list li{
        margin: 2px 7px;
        letter-spacing: 3px;
        font-size: 11px;
        color: black;
      
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 21rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 15px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 15px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 15px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 10px 0px;
        height: 28px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 2px 2px 2px 2px gray;
        border-radius: 5px;
      }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 1PX;
        font-size: 19px;
    }
    .of{
        height: 149px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 5px;
        height: 110px;
        box-shadow: 0px 2px 2px 0px gray;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 11px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        height: 17px;
        width: 28px;
        margin: 5px;
        background-color: rgb(238, 243, 241);
        background-image: url(left.png);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        border: 1px solid rgb(182, 179, 179);
        border-radius: 4px;
        box-shadow: 0px 2px 2px 0px gray;
        position: absolute;
        left: 93%;
      }
    .bttn:hover{
        height: 18px;
        width: 29px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:113px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 12px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 3em;
        background-color: black;
        color: white;
        margin-top: 20px;
      }
    .cop{
        text-align: center;
        font-size: 10px;
        letter-spacing: 2px;
        line-height: 19px;
    }
}
@media (min-width: 772px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 26px;
        margin: 4px;
        letter-spacing: 5px;
        text-shadow: 0px 1px 1px white;
        margin-left: 5px;
        width: 454px;
       
    }
    .title{
        margin: 5px;
        font-size: 15px;
        line-height: 1px;
        letter-spacing: 4px;
    }
    .Search{
        height: 0px;
        width: 0px;
        border-radius: 19px;
        margin: 0px;
        cursor: pointer;
        background: #fffefe;
        visibility: hidden;
    }
    .list{
        list-style: none;
        display: flex;
        width: 332px;
        visibility: visible;
    }
    .list li{
        margin: 4px 12px;
        letter-spacing: 3px;
        font-size: 14px;
        color: black;
      
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 28rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 20px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 20px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 20px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 20px 0px;
        height: 35px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 2px 2px 2px 2px gray;
        border-radius: 5px;
      }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 1PX;
        font-size: 21px;
    }
    .of{
        height: 188px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 5px;
        height: 139px;
        box-shadow: 0px 2px 2px 0px gray;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 13px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        height: 21px;
        width: 36px;
        margin: 6px;
        background-color: rgb(238, 243, 241);
        background-image: url(left.png);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        border: 1px solid rgb(182, 179, 179);
        border-radius: 4px;
        box-shadow: 0px 2px 2px 0px gray;
        position: absolute;
        left: 94%;
      }
    .bttn:hover{
        height: 22px;
        width: 37px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:142px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 15px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 4em;
        background-color: black;
        color: white;
        margin-top: 20px;
      }
    .cop{
        text-align: center;
        font-size: 12px;
        letter-spacing: 2px;
        line-height: 24px;
    }
}
@media (min-width: 992px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 27px;
        margin: 4px;
        letter-spacing: 5px;
        text-shadow: 0px 1px 1px white;
        margin-left: 5px;
        width: 635px;
       
    }
    .title{
        margin: 5px;
        font-size: 16px;
        line-height: 1px;
        letter-spacing: 4px;
    }
    .Search{
        height: 0px;
        width: 0px;
        border-radius: 19px;
        margin: 0px;
        cursor: pointer;
        background: #fffefe;
        visibility: hidden;
    }
    .list{
        list-style: none;
        display: flex;
        width: 464px;
    }
    .list li{
        margin: 4px 12px;
        letter-spacing: 3px;
        font-size: 15px;
        color: black;
      
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 33rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 25px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 25px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 25px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 20px 0px;
        height: 37px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 2px 2px 2px 2px gray;
        border-radius: 5px;
      }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 1PX;
        font-size: 23px;
    }
    .of{
        height: 210px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 8px;
        height: 160px;
        box-shadow: 0px 3px 3px 0px gray;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 14px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        height: 23px;
        width: 40px;
        margin: 6px;
        background-color: rgb(238, 243, 241);
        background-image: url(left.png);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        border: 1px solid rgb(182, 179, 179);
        border-radius: 4px;
        box-shadow: 0px 3px 3px 0px gray;
        position: absolute;
        left: 94.5%;
      }
    .bttn:hover{
        height: 24px;
        width: 40px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:164px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 16px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 4em;
        background-color: black;
        color: white;
        margin-top: 20px;
      }
    .cop{
        text-align: center;
        font-size: 12px;
        letter-spacing: 2px;
        line-height: 24px;
    }
}
@media (min-width: 1200px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 30px;
        margin: 5px 15px;
        letter-spacing: 4px;
        text-shadow: 0px 1px 1px white;
        width: 725px;
        
       
    }
    .title{
        margin: 5px 15px;
        font-size: 15px;
        line-height: 1px;
        letter-spacing: 2px;
    }
    .list{
        list-style: none;
        display: flex;
    }
    .list li{
        margin: 5px 40px;
        letter-spacing: 2px;
        font-size: 16px;
        color: black;
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 47rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 30px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 30px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 30px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 30px 0px;
        height: 35px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 3px 3px 3px 3px grey;
        border-radius: 7px;
    }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 2PX;
    }
    .of{
        height: 223px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 11px;
        padding: 0;
        height: 170px;
        box-shadow: 0px 5px 5px 0px grey;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        
        height: 22px;
        width: 38px;
        margin: 6px;
        background-color: rgb(238, 243, 241);
        background-image: url(left.png);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        border: 1px solid rgb(182, 179, 179);
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px gray;
        position: absolute;
        left: 95.7%;
    }
    .bttn:hover{
        height: 23px;
        width: 39px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:174px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 17px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 4em;
        background-color: black;
        color: white;
        margin-top: 20px;
    }
    .cop{
        text-align: center;
        line-height: 20px;
        letter-spacing: 3px;
    }
}
@media (min-width:1380px){
    *{
        margin: 0;
        padding: 0;
    }
    .header{
        display: flex;
        position: fixed;
        width: 100%;
    }
    .heading{
        font-size: 32px;
        margin: 5px 25px;
        letter-spacing: 4px;
        text-shadow: 0px 1px 1px white;
        width: 874px;
    }
    .title{
        margin: 5px 25px;
        font-size: 16px;
        line-height: 1px;
        letter-spacing: 2px;
    }
    .Search{
        height: 24px;
        width: 324px;
        border-radius: 19px;
        margin: 5px 110px;
        cursor: pointer;
        background: #fffefe;
    }
    .list{
        list-style: none;
        display: flex;
    }
    .list li{
        margin: 5px 40px;
        letter-spacing: 2px;
        font-size: 18px;
        color: black;
    }
    .pic{
        display: flex;
        
    }
    
    .bac{
        display: flex;
        height: 49rem;
        width: 100%;
        
    }
    .out{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .outfit{
        margin: 15px 70px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sports{
        margin: 15px 70px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .sunglass{
        margin: 15px 70px;
        box-shadow: 0px 5px 5px 0px gray;
    }
    .bar{
        margin: 30px 0px;
        height: 35px;
        width: 100%;
        background-color: rgb(245,255,250);
        box-shadow: 3px 3px 3px 3px grey;
        border-radius: 7px;
    }
    .txt{
        margin: 0px 10px;
        font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        letter-spacing: 2PX;
    }
    .of{
        height: 279px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        overflow: scroll;
        scroll-behavior: smooth;
    }
    img.t{
        margin: 11px;
        padding: 0;
        height: 177px;
        box-shadow: 0px 5px 5px 0px grey;
      
    }
    p.decc{
        display: block;
        text-align: center;
        font-family: sans-serif;
        font-size: 15px;
        margin: 0px 5px;
        color: black;
    }
    .bttn{
        
            height: 21px;
            width: 37px;
            margin: 8px;
            background-color: rgb(238, 243, 241);
            background-image: url(left.png);
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            border: 1px solid rgb(182, 179, 179);
            border-radius: 4px;
            box-shadow: 0px 5px 5px 0px gray;
            position: absolute;
            left: 96%;
    }
    .bttn:hover{
        height: 22px;
        width: 36px;
    }
    .bttn div{
        align-items: last baseline;
    }
    .t:hover{
        height:180px
    }
    .a{
        text-decoration: none;
    }
    ul li:hover{
        font-size: 19px;
        font-family: bold;
        color: rgb(26, 24, 24);
    }
    .fot{
        width: 100%;
        height: 4em;
        background-color: black;
        color: white;
        margin-top: 20px;
    }
    .cop{
        text-align: center;
    
    }
 }